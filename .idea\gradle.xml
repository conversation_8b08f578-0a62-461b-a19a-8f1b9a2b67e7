<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/@react-native/gradle-plugin" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-asset/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-constants/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-constants/scripts" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-file-system/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-font/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-keep-awake/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-modules-autolinking/android/expo-gradle-plugin" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-modules-autolinking/scripts/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-modules-core/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo-modules-core/expo-module-gradle-plugin" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/expo/scripts" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/react-native" />
        <option name="gradleJvm" value="#JAVA_HOME" />
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/react-native-edge-to-edge/android" />
        <option name="gradleJvm" value="#JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/node_modules/react-native-edge-to-edge/android" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>